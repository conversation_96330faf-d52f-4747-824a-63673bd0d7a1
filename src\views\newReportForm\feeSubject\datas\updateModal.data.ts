import { getAllDeptWithoutAuth } from '/@/api/common/dept'
import { getErpCateCode } from '/@/api/ERP/ERP'
import type { FormSchema } from '/@/components/Table'

/** 筛选 */
export const getSchemas: (type: 'add' | 'edit') => FormSchema[] = (type) => [
  {
    field: 'id',
    label: 'ID',
    component: 'InputNumber',
    ifShow: false
  },
  {
    field: 'fee',
    label: '费用名称',
    required: true,
    component: 'Input',
    dynamicDisabled: type === 'edit'
  },
  {
    field: 'debit',
    label: '借方科目',
    component: 'ApiSelect',
    required: true,
    itemProps: {
      validateTrigger: 'blur'
    },
    componentProps: ({}) => {
      return {
        api: getErpCateCode,
        resultField: 'items',
        selectProps: {
          showSearch: true,
          allowClear: true,
          placeholder: '请选择',
          labelInValue: true,
          fieldNames: { account_code: 'account_code', value: 'account_code', label: 'account_name' },
          optionFilterProp: 'account_name'
        }
      }
    }
  },
  {
    field: 'credit',
    label: '贷方科目',
    component: 'ApiSelect',
    required: true,
    itemProps: {
      validateTrigger: 'blur'
    },
    componentProps: ({}) => {
      return {
        api: getErpCateCode,
        resultField: 'items',
        selectProps: {
          showSearch: true,
          allowClear: true,
          placeholder: '请选择',
          labelInValue: true,
          fieldNames: { account_code: 'account_code', value: 'account_code', label: 'account_name' },
          optionFilterProp: 'account_name'
        }
      }
    }
  },
  {
    field: 'is_collect_fee',
    label: '是否启用收费',
    component: 'RadioButtonGroup',
    required: true,
    defaultValue: 0,
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ]
    }
  },
  {
    field: 'amount1',
    label: '收费金额(最小金额)',
    component: 'InputNumber',
    required({ model }) {
      return model.is_collect_fee == 1
    },
    componentProps: {
      min: 0,
      precision: 2
    },
    dynamicRules({ model }) {
      if (model.is_collect_fee == 1) {
        return [
          {
            validator: (_, value, callback) => {
              if (value > model.amount2) {
                callback('最小金额不能大于最大金额')
              } else if (value <= 0) {
                callback('最小金额不能小于0')
              } else if (value == model.amount2) {
                callback('最大金额不能等于最小金额')
              } else {
                callback()
              }
            }
          }
        ]
      } else {
        return []
      }
    }
  },
  {
    field: 'amount2',
    label: '收费金额(最大金额)',
    component: 'InputNumber',
    required({ model }) {
      return model.is_collect_fee == 1
    },
    componentProps: {
      min: 0,
      precision: 2
    },
    dynamicRules({ model }) {
      if (model.is_collect_fee == 1) {
        return [
          {
            validator: (_, value, callback) => {
              if (value < model.amount1) {
                callback('最大金额不能小于最小金额')
              } else if (value == model.amount1) {
                callback('最大金额不能等于最小金额')
              } else {
                callback()
              }
            }
          }
        ]
      } else {
        return []
      }
    }
  },
  {
    field: 'detp',
    label: `指派部门`,
    component: 'ApiSelect',
    itemProps: {
      validateTrigger: 'blur'
    },
    componentProps: {
      api: getAllDeptWithoutAuth,
      resultField: 'items',
      selectProps: {
        labelInValue: true,
        fieldNames: {
          key: 'key',
          value: 'id',
          label: 'name'
        },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        allowClear: true,
        style: {
          width: '100%'
        }
      }
    }
  },
  {
    // field: `group_dept_id`,
    field: 'top_dept',
    label: `顶级部门`,
    component: 'ApiSelect',
    itemProps: {
      validateTrigger: 'blur'
    },
    componentProps: {
      api: getAllDeptWithoutAuth,
      resultField: 'items',
      selectProps: {
        labelInValue: true,
        fieldNames: {
          key: 'key',
          value: 'id',
          label: 'name'
        },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        allowClear: true,
        style: {
          width: '100%'
        }
      }
    }
  },
  {
    field: 'rate',
    label: '费用率',
    component: 'InputNumber',
    componentProps: {
      min: 0,
      max: 100,
      precision: 4,
      addonAfter: '%'
    }
  },
  {
    // field: `group_dept_id`,
    field: 'income_dept_id',
    label: `收入部门`,
    component: 'ApiSelect',
    itemProps: {
      validateTrigger: 'blur'
    },
    componentProps: {
      api: getAllDeptWithoutAuth,
      resultField: 'items',
      selectProps: {
        // labelInValue: true,
        fieldNames: {
          key: 'key',
          value: 'id',
          label: 'name'
        },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        allowClear: true,
        style: {
          width: '100%'
        }
      }
    }
  }
]
