import dayjs from 'dayjs'
import { getDepartmentPermissionTree } from '/@/api/common/dept'
import { getUserList } from '/@/api/common/users'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { formateerNotCurrency } from '/@/utils/ERP/formatterPrice'
import { isNullOrUnDef } from '/@/utils/is'
import { max } from 'xe-utils'

export const INCOM_PATH = '/reportForm/income'
export const PAYMENT_PATH = '/reportForm/payment'

// 通用的列配置
const commonColumns: BasicColumn[] = [
  {
    title: '部门',
    dataIndex: 'dept_name',
    width: 120,
    resizable: true
  },
  {
    title: '关联的销售订单号',
    dataIndex: 'source_uniqid',
    width: 120,
    resizable: true
  },
  {
    title: '业务部门',
    dataIndex: 'operation_name',
    width: 120,
    resizable: true
  },
  {
    title: '渠道来源',
    dataIndex: 'source2',
    width: 120,
    resizable: true
  },
  {
    title: '项目名称',
    dataIndex: 'project_name',
    width: 120,
    resizable: true
  },
  {
    title: '方案负责人',
    dataIndex: 'program_incharge',
    width: 120,
    resizable: true
  },
  {
    title: '项目负责人',
    dataIndex: 'inCharge',
    width: 120,
    resizable: true
  },
  {
    title: '开单日期',
    dataIndex: 'submited_at',
    width: 120,
    resizable: true
  },
  {
    title: '结算日期',
    dataIndex: 'audit_at',
    width: 120,
    resizable: true
  },
  {
    title: '凤凰后金额',
    dataIndex: 'ph_amount',
    width: 120,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 120,
    resizable: true
  }
]

// 收入特有的列配置
export const incomeColumns: BasicColumn[] = [
  ...commonColumns,
  {
    title: '收款日期',
    dataIndex: 'collection_at',
    width: 120,
    resizable: true
  },
  {
    title: '收入金额',
    dataIndex: 'amount',
    width: 120,
    resizable: true,
    fixed: 'right',
    customRender: ({ value }) => {
      return !isNullOrUnDef(value) ? formateerNotCurrency.format(Number(value)) : '-'
    }
  },
  {
    title: '收入类型',
    dataIndex: 'type',
    width: 120,
    resizable: true,
    fixed: 'right'
  }
]

// 支出特有的列配置
export const paymentColumns: BasicColumn[] = [
  ...commonColumns,
  {
    title: '支出金额',
    dataIndex: 'amount',
    width: 120,
    resizable: true,
    fixed: 'right',
    customRender: ({ value }) => {
      return !isNullOrUnDef(value) ? formateerNotCurrency.format(Number(value)) : '-'
    }
  },
  {
    title: '支出类型',
    dataIndex: 'type',
    width: 120,
    resizable: true,
    fixed: 'right'
  }
]

// 根据路由路径返回对应的列配置
export const getColumns = (path: string): BasicColumn[] => {
  if (path === INCOM_PATH) {
    return incomeColumns
  } else if (path === PAYMENT_PATH) {
    return paymentColumns
  }
  return []
}

// 表单配置
export const getSchema: (path: string) => FormSchema[] = (path) => [
  {
    field: 'date',
    label: '年月',
    component: 'RangePicker',
    required: true,
    defaultValue: dayjs(),
    componentProps: {
      format: 'YYYY-MM-DD',
      style: {
        width: '100%'
      }
    }
  },
  {
    field: 'department_id',
    label: `部门(单选)`,
    component: 'ApiTreeSelect',
    componentProps: ({ formModel }) => ({
      resultField: 'items',
      immediate: false,
      lazyLoad: true,
      // labelInValue: true,
      api: getDepartmentPermissionTree,
      treeSelectProps: {
        treeDataSimpleMode: true,
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        // treeCheckable: true,
        // showCheckedStrategy: 'SHOW_ALL',
        treeDefaultExpandAll: true,
        showSearch: true,
        treeLine: {
          showLeafIcon: false
        },
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        },
        onChange: () => {
          formModel.department_ids = undefined
        }
      }
    })
  },
  {
    field: 'department_ids',
    label: `部门(多选)`,
    component: 'ApiTreeSelect',
    componentProps: ({ formModel }) => ({
      resultField: 'items',
      immediate: false,
      lazyLoad: true,
      labelInValue: true,
      maxTagCount: 3,
      api: getDepartmentPermissionTree,
      treeSelectProps: {
        treeDataSimpleMode: true,
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        treeCheckable: true,
        showCheckedStrategy: 'SHOW_ALL',
        treeDefaultExpandAll: true,
        showSearch: true,
        treeLine: {
          showLeafIcon: false
        },
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        },
        onChange: () => {
          formModel.department_id = undefined
        }
      }
    })
  },
  {
    field: 'source_uniqid',
    label: '关联的销售订单号',
    component: 'Input'
  },
  {
    field: 'operation_id',
    label: '业务部门',
    component: 'ApiTreeSelect',
    componentProps: ({ formModel }) => ({
      resultField: 'items',
      immediate: false,
      lazyLoad: true,
      // labelInValue: true,
      api: getDepartmentPermissionTree,
      treeSelectProps: {
        treeDataSimpleMode: true,
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        // treeCheckable: true,
        // showCheckedStrategy: 'SHOW_ALL',
        treeDefaultExpandAll: true,
        showSearch: true,
        treeLine: {
          showLeafIcon: false
        },
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        },
        onChange() {
          formModel.operation_ids = undefined
        }
      }
    })
  },
  {
    field: 'operation_ids',
    label: '业务部门',
    component: 'ApiTreeSelect',
    componentProps: ({ formModel }) => ({
      resultField: 'items',
      immediate: false,
      lazyLoad: true,
      labelInValue: true,
      maxTagCount: 3,
      api: getDepartmentPermissionTree,
      treeSelectProps: {
        treeDataSimpleMode: true,
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        treeCheckable: true,
        showCheckedStrategy: 'SHOW_ALL',
        treeDefaultExpandAll: true,
        showSearch: true,
        treeLine: {
          showLeafIcon: false
        },
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        },
        onChange() {
          formModel.operation_id = undefined
        }
      }
    })
  },
  {
    field: 'source2',
    label: '渠道来源',
    component: 'Input'
  },
  {
    field: 'project_name',
    label: '项目名称',
    component: 'Input'
  },
  {
    field: 'program_incharge',
    label: '方案负责人',
    component: 'ApiSelect',
    componentProps: {
      api: getUserList,
      resultField: 'items',
      selectProps: {
        fieldNames: {
          key: 'key',
          value: 'id',
          label: 'name'
        },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        allowClear: true,
        style: {
          width: '100%'
        }
      }
    }
  },
  {
    field: 'inCharge',
    label: '项目负责人',
    component: 'ApiSelect',
    componentProps: {
      api: getUserList,
      resultField: 'items',
      selectProps: {
        fieldNames: {
          key: 'key',
          value: 'id',
          label: 'name'
        },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        allowClear: true,
        style: {
          width: '100%'
        }
      }
    }
  },
  {
    field: 'submited_at',
    label: '开单日期',
    component: 'RangePicker'
  },
  {
    field: 'audit_at',
    label: '结算日期',
    component: 'RangePicker'
  },
  {
    field: 'type',
    label: (path === INCOM_PATH ? '收入' : '支出') + '类型',
    component: 'Input'
  }
]
